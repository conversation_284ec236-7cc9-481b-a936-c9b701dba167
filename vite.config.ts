import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Enable Fast Refresh for better hot reload
      fastRefresh: true,
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  server: {
    // Hot reload configuration
    hmr: {
      overlay: true, // Show error overlay
    },
    // Auto-open browser
    open: true,
    // Port configuration
    port: 3000,
    // Enable CORS for development
    cors: true,
    // Watch for changes in these file types
    watch: {
      usePolling: false, // Use native file watching for better performance
      interval: 100, // Check for changes every 100ms
    },
  },
  // Build optimizations that also help with dev performance
  esbuild: {
    // Enable source maps for better debugging
    sourcemap: true,
  },
});