import React from 'react';
import { Instagram, Facebook, Linkedin, Twitter, Youtube, MessageCircle } from 'lucide-react';
import BlurFade from './ui/blur-fade';

const SocialMediaSection = () => {
  const socialPlatforms = [
    {
      name: 'Instagram',
      icon: Instagram,
      color: 'bg-gradient-to-br from-purple-500 to-pink-500',
      followers: '2.5K',
      handle: '@stokeleads'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'bg-blue-600',
      followers: '1.8K',
      handle: 'StokeLeads'
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      color: 'bg-blue-700',
      followers: '950',
      handle: 'StokeLeads'
    },
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'bg-sky-500',
      followers: '1.2K',
      handle: '@stokeleads'
    },
    {
      name: 'YouTube',
      icon: Youtube,
      color: 'bg-red-600',
      followers: '850',
      handle: 'StokeLeads'
    },
    {
      name: 'TikT<PERSON>',
      icon: MessageCircle,
      color: 'bg-black',
      followers: '3.1K',
      handle: '@stokeleads'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <BlurFade delay={0.1}>
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Follow Our{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                Success Stories
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Join thousands of contractors who follow us for daily tips, success stories, 
              and behind-the-scenes content. See real results from real businesses.
            </p>
          </div>
        </BlurFade>

        {/* Orbiting Circles Animation */}
        <BlurFade delay={0.2}>
          <div className="relative flex h-[500px] w-full items-center justify-center overflow-hidden">
            {/* Dashed Orbit Paths - Darker */}
            <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 500 500">
              {/* Inner orbit path */}
              <circle
                cx="250"
                cy="250"
                r="80"
                fill="none"
                stroke="#9ca3af"
                strokeWidth="1.5"
                strokeDasharray="8,8"
                opacity="0.6"
              />
              {/* Middle orbit path */}
              <circle
                cx="250"
                cy="250"
                r="140"
                fill="none"
                stroke="#9ca3af"
                strokeWidth="1.5"
                strokeDasharray="12,12"
                opacity="0.5"
              />
              {/* Outer orbit path */}
              <circle
                cx="250"
                cy="250"
                r="200"
                fill="none"
                stroke="#9ca3af"
                strokeWidth="1.5"
                strokeDasharray="16,16"
                opacity="0.4"
              />
            </svg>

            {/* Center Logo/Brand - Smaller with more padding */}
            <div className="relative z-10 flex h-28 w-28 items-center justify-center rounded-full bg-white shadow-2xl border-4 border-orange-500 overflow-hidden p-3">
              <img 
                src="/square logo icon (3).webp" 
                alt="StokeLeads Logo" 
                className="h-full w-full object-contain"
              />
            </div>

            {/* First Orbit - Inner Circle */}
            {socialPlatforms.slice(0, 2).map((platform, index) => {
              const Icon = platform.icon;
              return (
                <div
                  key={`inner-${index}`}
                  className="absolute animate-orbit"
                  style={{
                    '--duration': '20',
                    '--radius': '80',
                    animationDelay: `${index * -10}s`,
                  } as React.CSSProperties}
                >
                  <div className={`flex h-10 w-10 items-center justify-center rounded-full ${platform.color} shadow-lg hover:scale-110 transition-all duration-300 cursor-pointer group border-2 border-white`}>
                    <Icon className="h-5 w-5 text-white" />
                    <div className="absolute -bottom-10 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 bg-gray-900 text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap z-20 shadow-lg">
                      <div className="font-semibold">{platform.name}</div>
                      <div className="text-gray-300">{platform.followers} followers</div>
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                    </div>
                  </div>
                </div>
              );
            })}

            {/* Second Orbit - Middle Circle */}
            {socialPlatforms.slice(2, 4).map((platform, index) => {
              const Icon = platform.icon;
              return (
                <div
                  key={`middle-${index}`}
                  className="absolute animate-orbit"
                  style={{
                    '--duration': '30',
                    '--radius': '140',
                    animationDelay: `${index * -15}s`,
                    animationDirection: 'reverse',
                  } as React.CSSProperties}
                >
                  <div className={`flex h-12 w-12 items-center justify-center rounded-full ${platform.color} shadow-lg hover:scale-110 transition-all duration-300 cursor-pointer group border-2 border-white`}>
                    <Icon className="h-6 w-6 text-white" />
                    <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 bg-gray-900 text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap z-20 shadow-lg">
                      <div className="font-semibold">{platform.name}</div>
                      <div className="text-gray-300">{platform.followers} followers</div>
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                    </div>
                  </div>
                </div>
              );
            })}

            {/* Third Orbit - Outer Circle */}
            {socialPlatforms.slice(4, 6).map((platform, index) => {
              const Icon = platform.icon;
              return (
                <div
                  key={`outer-${index}`}
                  className="absolute animate-orbit"
                  style={{
                    '--duration': '40',
                    '--radius': '200',
                    animationDelay: `${index * -20}s`,
                  } as React.CSSProperties}
                >
                  <div className={`flex h-14 w-14 items-center justify-center rounded-full ${platform.color} shadow-lg hover:scale-110 transition-all duration-300 cursor-pointer group border-2 border-white`}>
                    <Icon className="h-7 w-7 text-white" />
                    <div className="absolute -bottom-14 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 bg-gray-900 text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap z-20 shadow-lg">
                      <div className="font-semibold">{platform.name}</div>
                      <div className="text-gray-300">{platform.followers} {platform.name === 'YouTube' ? 'subscribers' : 'followers'}</div>
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                    </div>
                  </div>
                </div>
              );
            })}

            {/* Subtle glow effect around center */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-40 h-40 bg-gradient-to-r from-orange-400/10 to-blue-400/10 rounded-full blur-xl"></div>
            </div>
          </div>
        </BlurFade>

        {/* Social Media Stats */}
        <BlurFade delay={0.3}>
          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <div className="text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="text-4xl font-bold text-blue-600 mb-2">10K+</div>
              <div className="text-gray-800 font-semibold text-lg">Total Followers</div>
              <p className="text-sm text-gray-500 mt-2">Across all platforms</p>
            </div>
            
            <div className="text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="text-4xl font-bold text-purple-600 mb-2">500+</div>
              <div className="text-gray-800 font-semibold text-lg">Success Stories Shared</div>
              <p className="text-sm text-gray-500 mt-2">Real contractor transformations</p>
            </div>
            
            <div className="text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
              <div className="text-4xl font-bold text-orange-600 mb-2">Daily</div>
              <div className="text-gray-800 font-semibold text-lg">Tips & Updates</div>
              <p className="text-sm text-gray-500 mt-2">Fresh content every day</p>
            </div>
          </div>
        </BlurFade>

        {/* Call to Action */}
        <BlurFade delay={0.4}>
          <div className="text-center mt-16">
            <div className="bg-white rounded-2xl shadow-xl p-8 max-w-2xl mx-auto border border-gray-100">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Stay Connected & Get Inspired
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Follow us on your favorite platform to see daily success stories, get business tips, 
                and stay updated on the latest features that can help grow your contractor business.
              </p>
              
              <div className="flex flex-wrap justify-center gap-3">
                {socialPlatforms.slice(0, 4).map((platform, index) => {
                  const Icon = platform.icon;
                  return (
                    <button
                      key={index}
                      className={`flex items-center space-x-2 ${platform.color} text-white px-5 py-3 rounded-xl font-semibold hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl`}
                    >
                      <Icon className="w-4 h-4" />
                      <span>Follow</span>
                    </button>
                  );
                })}
              </div>
              
              <div className="mt-6 text-sm text-gray-500">
                Join thousands of contractors already following our journey
              </div>
            </div>
          </div>
        </BlurFade>
      </div>
    </section>
  );
};

export default SocialMediaSection;