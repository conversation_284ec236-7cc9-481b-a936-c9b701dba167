import React from 'react';
import { Smartphone, Bot, Star, Phone, MessageSquare, Calendar, CreditCard, BarChart3 } from 'lucide-react';

const Solution = () => {
  const features = [
    {
      icon: Smartphone,
      title: "Modern, Mobile-Responsive Website",
      description: "Get a professional, high-converting website built with AI in minutes. It looks great and functions perfectly on any device, ensuring your potential clients have a smooth experience and encouraging them to convert.",
      color: "bg-blue-500"
    },
    {
      icon: Bo<PERSON>,
      title: "Automated Lead Capture & Nurture",
      description: "Our sites include an AI chatbot and functional lead forms that instantly capture inquiries and start conversations via text message, funnelling new leads directly to your dedicated mobile app.",
      color: "bg-teal-500"
    },
    {
      icon: Star,
      title: "5-Star Google Review Automation",
      description: "Automatically request reviews after every job, directing satisfied customers to leave public 5-star reviews while handling negative feedback privately. This boosts your Google ranking and brings more customers through Google.",
      color: "bg-yellow-500"
    },
    {
      icon: Phone,
      title: "Auto Call Text Back for Missed Calls",
      description: "Never miss a lead again. If you miss a phone call, our system automatically texts the caller back with an offer to book an estimate or visit your website, keeping the conversation alive.",
      color: "bg-green-500"
    },
    {
      icon: MessageSquare,
      title: "Unified Mobile App Inbox",
      description: "All leads from your website, reviews, missed calls, and even social media messages flow into one intuitive app on your phone. This mobile app is what creates a sticky recurring service because this is what you'll use every day to run your business.",
      color: "bg-purple-500"
    },
    {
      icon: BarChart3,
      title: "Text Remarketing & Customer Reactivation",
      description: "Automatically follow up with past clients or old lead lists to drive repeat business and referrals with targeted text campaigns, helping you reactivate them and then have the AI bot book the appointment.",
      color: "bg-orange-500"
    },
    {
      icon: Calendar,
      title: "Streamlined Calendar Integration",
      description: "Integrate booking directly into your system, making it easy to schedule estimates and manage your appointments all in one place.",
      color: "bg-indigo-500"
    },
    {
      icon: CreditCard,
      title: "Payment Processing",
      description: "Accept payments directly through your system with secure, integrated payment processing that makes it easy for customers to pay you.",
      color: "bg-pink-500"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Transform Your Online Presence &{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-teal-600">
              Grow Your Business on Autopilot
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
            We don't just build websites; we provide a complete, AI-powered system designed to get you more jobs 
            and simplify your day-to-day operations. This system leverages pre-built software systems that work 
            together to bring local businesses insane results at an insane price point.
          </p>
          
          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-teal-50 px-6 py-3 rounded-full border border-blue-200">
            <Bot className="w-6 h-6 text-blue-600" />
            <span className="text-blue-800 font-semibold">Your Complete AI-Powered Business System</span>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <div key={index} className="group bg-gray-50 hover:bg-white rounded-2xl p-8 transition-all duration-300 hover:shadow-xl border border-gray-100 hover:border-gray-200">
              <div className="flex items-start space-x-4">
                <div className={`flex-shrink-0 w-14 h-14 ${feature.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-7 h-7 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* System Overview */}
        <div className="bg-gradient-to-br from-blue-900 to-teal-800 rounded-3xl p-8 md:p-12 text-white">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl md:text-4xl font-bold mb-6">
                Everything Works Together Seamlessly
              </h3>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                Your website, lead capture, review automation, missed call text-back, and mobile app 
                all work as one unified system to maximize your business growth.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                  <span className="text-blue-100">Lead visits your website</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                  <span className="text-blue-100">AI chatbot engages and captures their info</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">3</span>
                  </div>
                  <span className="text-blue-100">Lead flows to your mobile app instantly</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">4</span>
                  </div>
                  <span className="text-blue-100">You close the deal and get paid</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">5</span>
                  </div>
                  <span className="text-blue-100">System automatically requests 5-star review</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white rounded-2xl p-6 shadow-2xl">
                <div className="text-gray-800 space-y-4">
                  <div className="flex items-center justify-between border-b pb-3">
                    <span className="font-bold text-lg">StokeLeads Mobile App</span>
                    <div className="flex space-x-1">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <MessageSquare className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-sm">New Website Lead</p>
                        <p className="text-xs text-gray-600">Kitchen remodel inquiry</p>
                      </div>
                      <span className="text-xs text-green-600 font-medium">Just now</span>
                    </div>
                    
                    <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Phone className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-sm">Missed Call Follow-up</p>
                        <p className="text-xs text-gray-600">Auto text sent</p>
                      </div>
                      <span className="text-xs text-blue-600 font-medium">2 min ago</span>
                    </div>
                    
                    <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                      <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                        <Star className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-sm">Review Request Sent</p>
                        <p className="text-xs text-gray-600">Johnson project completed</p>
                      </div>
                      <span className="text-xs text-yellow-600 font-medium">1 hour ago</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Solution;