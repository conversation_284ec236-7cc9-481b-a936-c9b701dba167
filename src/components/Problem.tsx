import React from 'react';
import { AlertTriangle, Smartphone, PhoneOff, MessageSquareX } from 'lucide-react';

const Problem = () => {
  const problems = [
    {
      icon: PhoneOff,
      title: "Non-Clickable Phone Numbers",
      description: "Customers can't easily call you from mobile devices"
    },
    {
      icon: MessageSquareX,
      title: "Broken Contact Forms",
      description: "Lead forms that disappear into 'outer space'"
    },
    {
      icon: Smartphone,
      title: "Not Mobile-Friendly",
      description: "Unreadable and unusable on phones and tablets"
    },
    {
      icon: AlertTriangle,
      title: "Outdated Design",
      description: "Makes your business look unprofessional and outdated"
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            The Hidden Costs of an{' '}
            <span className="text-red-600">Outdated Website</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Many contractors have websites that are stuck in the past. They're often clunky, not mobile-friendly, 
            and riddled with broken features. This means missed calls, lost leads, and money left on the table.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {problems.map((problem, index) => (
            <div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100">
              <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4 mx-auto">
                <problem.icon className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 text-center mb-3">
                {problem.title}
              </h3>
              <p className="text-gray-600 text-center leading-relaxed">
                {problem.description}
              </p>
            </div>
          ))}
        </div>

        {/* Before/After Comparison */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="grid md:grid-cols-2">
            {/* Before */}
            <div className="p-8 bg-red-50 border-r border-gray-200">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-600" />
                </div>
                <h3 className="text-2xl font-bold text-red-800 mb-2">Outdated Website</h3>
                <p className="text-red-600">What's costing you business</p>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✗</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Broken on Mobile</p>
                    <p className="text-sm text-gray-600">70% of users can't use your site properly</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✗</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Missed Leads</p>
                    <p className="text-sm text-gray-600">Contact forms go nowhere</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✗</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Poor First Impression</p>
                    <p className="text-sm text-gray-600">Customers question your professionalism</p>
                  </div>
                </div>
              </div>
            </div>

            {/* After */}
            <div className="p-8 bg-green-50">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                  <span className="text-2xl">✨</span>
                </div>
                <h3 className="text-2xl font-bold text-green-800 mb-2">AI-Powered Website</h3>
                <p className="text-green-600">What you get with StokeLeads</p>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Perfect on All Devices</p>
                    <p className="text-sm text-gray-600">Beautiful and functional everywhere</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Never Miss a Lead</p>
                    <p className="text-sm text-gray-600">Automated capture and follow-up</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Professional & Modern</p>
                    <p className="text-sm text-gray-600">Builds trust and credibility instantly</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mt-12">
          <p className="text-xl text-gray-700 font-medium">
            You're too busy with your craft to worry about website tech –{' '}
            <span className="text-blue-600 font-semibold">but you shouldn't be losing customers because of it.</span>
          </p>
        </div>
      </div>
    </section>
  );
};

export default Problem;