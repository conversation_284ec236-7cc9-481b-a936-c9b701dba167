import React, { useState } from 'react';
import { ExternalLink, Eye } from 'lucide-react';

const Portfolio = () => {
  const [activeCategory, setActiveCategory] = useState('all');

  const templates = [
    {
      id: 1,
      title: "Elite Roofing Solutions",
      category: "roofing",
      image: "https://images.pexels.com/photos/186077/pexels-photo-186077.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Modern roofing contractor website with lead capture and review automation",
      features: ["Lead Forms", "Review System", "Mobile Responsive"]
    },
    {
      id: 2,
      title: "Pro Landscaping Co",
      category: "landscaping",
      image: "https://images.pexels.com/photos/1301856/pexels-photo-1301856.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Beautiful landscaping website showcasing portfolio and services",
      features: ["Portfolio Gallery", "Quote Calculator", "AI Chatbot"]
    },
    {
      id: 3,
      title: "Master Plumbing Services",
      category: "plumbing",
      image: "https://images.pexels.com/photos/8486944/pexels-photo-8486944.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Professional plumbing website with emergency service features",
      features: ["24/7 Booking", "Emergency Contact", "Service Areas"]
    },
    {
      id: 4,
      title: "Precision HVAC Systems",
      category: "hvac",
      image: "https://images.pexels.com/photos/3964341/pexels-photo-3964341.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "HVAC contractor site with maintenance scheduling and diagnostics",
      features: ["Maintenance Plans", "Diagnostic Tools", "Energy Savings"]
    },
    {
      id: 5,
      title: "Artisan Kitchen Remodeling",
      category: "remodeling",
      image: "https://images.pexels.com/photos/1080721/pexels-photo-1080721.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Elegant remodeling website with before/after galleries",
      features: ["Before/After Gallery", "Design Consultation", "Material Calculator"]
    },
    {
      id: 6,
      title: "Guardian Home Security",
      category: "security",
      image: "https://images.pexels.com/photos/430208/pexels-photo-430208.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Security contractor website with monitoring and installation services",
      features: ["Security Assessment", "Monitoring Plans", "Installation Booking"]
    }
  ];

  const categories = [
    { id: 'all', name: 'All Templates' },
    { id: 'roofing', name: 'Roofing' },
    { id: 'landscaping', name: 'Landscaping' },
    { id: 'plumbing', name: 'Plumbing' },
    { id: 'hvac', name: 'HVAC' },
    { id: 'remodeling', name: 'Remodeling' },
    { id: 'security', name: 'Security' }
  ];

  const filteredTemplates = activeCategory === 'all' 
    ? templates 
    : templates.filter(template => template.category === activeCategory);

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Browse Our Templates &{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-teal-600">
              Client Examples
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
            We specialize in creating high-converting websites for businesses like yours. 
            Explore some examples of our AI-generated templates and functional client sites. 
            The websites actually look really good as well.
          </p>
          
          <div className="inline-flex items-center space-x-2 bg-white px-6 py-3 rounded-full shadow-lg border border-gray-200">
            <Eye className="w-5 h-5 text-blue-600" />
            <span className="text-gray-700 font-medium">All sites built for functionality, lead capture, and mobile responsiveness</span>
          </div>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Templates Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredTemplates.map((template) => (
            <div key={template.id} className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              {/* Image */}
              <div className="relative overflow-hidden h-48">
                <img 
                  src={template.image} 
                  alt={template.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4 flex space-x-2">
                    <button className="flex-1 bg-white text-gray-900 px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2">
                      <Eye className="w-4 h-4" />
                      <span>Preview</span>
                    </button>
                    <button className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold text-sm hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2">
                      <ExternalLink className="w-4 h-4" />
                      <span>View Live</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {template.title}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {template.description}
                </p>
                
                {/* Features */}
                <div className="flex flex-wrap gap-2">
                  {template.features.map((feature, index) => (
                    <span 
                      key={index}
                      className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full font-medium"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Get Your Own High-Converting Website?
            </h3>
            <p className="text-xl text-blue-100 mb-6 max-w-2xl mx-auto">
              All sites are built for functionality, lead capture, and mobile responsiveness, 
              helping convert visitors to paying customers.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg">
              Get My FREE Website Now
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;