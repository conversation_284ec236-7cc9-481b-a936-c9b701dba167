"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import React, { forwardRef, useRef } from "react";

export interface AnimatedBeamProps {
  className?: string;
  containerRef: React.RefObject<HTMLElement>;
  fromRef: React.RefObject<HTMLElement>;
  toRef: React.RefObject<HTMLElement>;
  curvature?: number;
  reverse?: boolean;
  pathColor?: string;
  pathWidth?: number;
  pathOpacity?: number;
  gradientStartColor?: string;
  gradientStopColor?: string;
  delay?: number;
  duration?: number;
  startXOffset?: number;
  startYOffset?: number;
  endXOffset?: number;
  endYOffset?: number;
}

export const AnimatedBeam = forwardRef<SVGSVGElement, AnimatedBeamProps>(
  (
    {
      className,
      containerRef,
      fromRef,
      toRef,
      curvature = 0,
      reverse = false,
      duration = 4,
      delay = 0,
      pathColor = "#d1d5db",
      pathWidth = 2,
      pathOpacity = 0.5,
      gradientStartColor = "#ffaa40",
      gradientStopColor = "#9c40ff",
      startXOffset = 0,
      startYOffset = 0,
      endXOffset = 0,
      endYOffset = 0,
    },
    ref,
  ) => {
    const id = React.useId();
    const svgRef = useRef<SVGSVGElement>(null);
    const pathRef = useRef<SVGPathElement>(null);

    const [pathD, setPathD] = React.useState("");
    const [svgDimensions, setSvgDimensions] = React.useState({ width: 0, height: 0 });

    const updatePath = React.useCallback(() => {
      if (containerRef.current && fromRef.current && toRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const rectA = fromRef.current.getBoundingClientRect();
        const rectB = toRef.current.getBoundingClientRect();

        const svgWidth = containerRect.width;
        const svgHeight = containerRect.height;
        setSvgDimensions({ width: svgWidth, height: svgHeight });

        const startX = rectA.left - containerRect.left + rectA.width / 2 + startXOffset;
        const startY = rectA.top - containerRect.top + rectA.height / 2 + startYOffset;
        const endX = rectB.left - containerRect.left + rectB.width / 2 + endXOffset;
        const endY = rectB.top - containerRect.top + rectB.height / 2 + endYOffset;

        const controlPointX = startX + (endX - startX) / 2;
        const controlPointY = startY + (endY - startY) / 2 - curvature;

        const d = `M ${startX},${startY} Q ${controlPointX},${controlPointY} ${endX},${endY}`;
        setPathD(d);
      }
    }, [containerRef, fromRef, toRef, curvature, startXOffset, startYOffset, endXOffset, endYOffset]);

    React.useEffect(() => {
      updatePath();
      window.addEventListener("resize", updatePath);
      return () => window.removeEventListener("resize", updatePath);
    }, [updatePath]);

    return (
      <svg
        ref={ref || svgRef}
        className={cn(
          "pointer-events-none absolute left-0 top-0 transform-gpu",
          className,
        )}
        width={svgDimensions.width}
        height={svgDimensions.height}
        viewBox={`0 0 ${svgDimensions.width} ${svgDimensions.height}`}
        style={{
          transform: "translateZ(0)",
        }}
      >
        <defs>
          <linearGradient
            className={cn("transform-gpu")}
            id={`${id}-gradient`}
            gradientUnits="userSpaceOnUse"
            x1="0%"
            x2="100%"
            y1="0"
            y2="0"
          >
            <stop stopColor={gradientStartColor} stopOpacity="0" />
            <stop offset="20%" stopColor={gradientStartColor} stopOpacity="1" />
            <stop offset="80%" stopColor={gradientStopColor} stopOpacity="1" />
            <stop offset="100%" stopColor={gradientStopColor} stopOpacity="0" />
          </linearGradient>
          
          {/* Glowing effect filter */}
          <filter id={`${id}-glow`} x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        {/* Persistent grey path - always visible */}
        <path
          d={pathD}
          stroke={pathColor}
          strokeWidth={pathWidth}
          strokeOpacity={pathOpacity}
          fill="none"
          strokeLinecap="round"
        />
        
        {/* Animated glowing motion along the path */}
        <motion.path
          d={pathD}
          stroke={`url(#${id}-gradient)`}
          strokeWidth={pathWidth + 1}
          fill="none"
          strokeLinecap="round"
          filter={`url(#${id}-glow)`}
          initial={{
            strokeDasharray: "0 1000",
            strokeDashoffset: 0,
          }}
          animate={{
            strokeDasharray: "100 900",
            strokeDashoffset: -1000,
          }}
          transition={{
            delay,
            duration: duration,
            ease: "easeInOut",
            repeat: Infinity,
            repeatDelay: 3,
          }}
        />
      </svg>
    );
  },
);

AnimatedBeam.displayName = "AnimatedBeam";