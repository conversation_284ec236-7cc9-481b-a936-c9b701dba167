import React from "react";
import { motion } from "framer-motion";
import Iphone15Pro from "@/components/magicui/iphone-15-pro";

const PhoneMockup = React.forwardRef<HTMLDivElement, { className?: string }>(
  ({ className }, ref) => {
    const notifications = [
      {
        id: 1,
        icon: "📧",
        title: "New Email Lead",
        message: "Kitchen renovation inquiry from <PERSON>",
        time: "2m ago",
        delay: 1,
      },
      {
        id: 2,
        icon: "💬",
        title: "SMS Message",
        message: "Bathroom quote request - urgent",
        time: "5m ago",
        delay: 2,
      },
      {
        id: 3,
        icon: "📱",
        title: "Facebook Message",
        message: "Deck building project discussion",
        time: "8m ago",
        delay: 3,
      },
      {
        id: 4,
        icon: "📸",
        title: "Instagram DM",
        message: "Loved your recent project! Can we chat?",
        time: "12m ago",
        delay: 4,
      },
    ];

    return (
      <div ref={ref} className={className}>
        <Iphone15Pro className="w-24 h-32 md:w-32 md:h-40">
          {/* Phone Screen Content */}
          <div className="w-full h-full bg-gradient-to-br from-blue-50 to-blue-100 relative overflow-hidden">
            {/* Status Bar */}
            <div className="flex justify-between items-center px-4 py-2 text-xs text-gray-800">
              <span className="font-medium">9:41</span>
              <div className="flex items-center gap-1">
                <div className="w-4 h-2 border border-gray-800 rounded-sm">
                  <div className="w-3 h-1 bg-gray-800 rounded-sm m-0.5"></div>
                </div>
              </div>
            </div>

            {/* App Header */}
            <div className="px-4 py-2 bg-white/80 backdrop-blur-sm border-b border-gray-200">
              <h1 className="text-sm font-bold text-gray-900">StokeLeads</h1>
              <p className="text-xs text-gray-600">All Messages</p>
            </div>

            {/* Notifications */}
            <div className="p-2 space-y-1">
              {notifications.map((notification) => (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, x: 50, scale: 0.8 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  transition={{
                    delay: notification.delay,
                    duration: 0.5,
                    ease: "easeOut",
                    repeat: Infinity,
                    repeatDelay: 8,
                  }}
                  className="bg-white rounded-lg p-2 shadow-sm border border-gray-100"
                >
                  <div className="flex items-start gap-2">
                    <span className="text-xs">{notification.icon}</span>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-xs font-medium text-gray-900 truncate">
                          {notification.title}
                        </p>
                        <span className="text-xs text-gray-500">
                          {notification.time}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 truncate mt-0.5">
                        {notification.message}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Bottom indicator */}
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2">
              <div className="w-8 h-1 bg-gray-400 rounded-full"></div>
            </div>
          </div>
        </Iphone15Pro>
      </div>
    );
  }
);

PhoneMockup.displayName = "PhoneMockup";

export default PhoneMockup;
