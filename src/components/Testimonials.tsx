import React from 'react';
import { Star, Quote } from 'lucide-react';
import BlurFade from './ui/blur-fade';
import Marquee from './ui/marquee';

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      business: "Johnson Roofing LLC",
      location: "Dallas, TX",
      image: "https://images.pexels.com/photos/1043474/pexels-photo-1043474.jpeg?auto=compress&cs=tinysrgb&w=400",
      quote: "Since getting our new website and automated review system, our Google presence has exploded, and we're getting more leads than ever! We went from 3-4 jobs a month to booking 15-20. The AI chatbot alone has captured leads we would have completely missed.",
      rating: 5,
      results: "5x increase in monthly jobs"
    },
    {
      name: "<PERSON>",
      business: "Elite Landscaping Services",
      location: "Phoenix, AZ",
      image: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400",
      quote: "The mobile app changed everything. All my client messages and leads in one place, easily responding on the go. Saved me so much time! I can manage my entire business from my phone now. The text remarketing feature alone brought back $15,000 in old customers.",
      rating: 5,
      results: "$15K in reactivated customers"
    },
    {
      name: "Tom Rodriguez",
      business: "Rodriguez Plumbing Co",
      location: "Miami, FL",
      image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400",
      quote: "No more missed calls! The auto text-back feature is brilliant. It's like having a receptionist 24/7. Even when I'm on a job site, the system keeps working for me. My conversion rate from missed calls went from 0% to over 60%.",
      rating: 5,
      results: "60% missed call conversion rate"
    },
    {
      name: "Jennifer Chen",
      business: "Precision HVAC Solutions",
      location: "Chicago, IL",
      image: "https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=400",
      quote: "The 5-star review automation is incredible. We went from having 12 Google reviews to over 150 in just 6 months. Our Google ranking improved dramatically and now we show up first for most searches in our area. The ROI has been amazing.",
      rating: 5,
      results: "12 to 150+ Google reviews in 6 months"
    },
    {
      name: "David Thompson",
      business: "Thompson Kitchen Remodeling",
      location: "Seattle, WA",
      image: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=400",
      quote: "I was skeptical about the 'free website' offer, but after seeing the results, I'm a believer. The system pays for itself within the first week every month. The lead quality is so much better than anything I've tried before.",
      rating: 5,
      results: "ROI within first week monthly"
    },
    {
      name: "Lisa Anderson",
      business: "Guardian Home Security",
      location: "Denver, CO",
      image: "https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400",
      quote: "The website looks incredibly professional and the booking system makes it so easy for customers to schedule consultations. The calendar integration alone has saved me hours every week. My business has never been more organized.",
      rating: 5,
      results: "Hours saved weekly on scheduling"
    }
  ];

  // Split testimonials for marquee
  const firstRow = testimonials.slice(0, testimonials.length / 2);
  const secondRow = testimonials.slice(testimonials.length / 2);

  const TestimonialCard = ({ testimonial }: { testimonial: typeof testimonials[0] }) => (
    <div className="bg-gray-50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 relative w-96 mx-4">
      {/* Quote Icon */}
      <div className="absolute top-6 right-6 text-blue-200">
        <Quote className="w-8 h-8" />
      </div>

      {/* Stars */}
      <div className="flex space-x-1 mb-6">
        {[...Array(testimonial.rating)].map((_, i) => (
          <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
        ))}
      </div>

      {/* Quote */}
      <blockquote className="text-gray-700 mb-6 leading-relaxed line-clamp-4">
        "{testimonial.quote}"
      </blockquote>

      {/* Results Badge */}
      <div className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold mb-6 inline-block">
        {testimonial.results}
      </div>

      {/* Author */}
      <div className="flex items-center space-x-4">
        <img 
          src={testimonial.image} 
          alt={testimonial.name}
          className="w-12 h-12 rounded-full object-cover"
        />
        <div>
          <div className="font-bold text-gray-900">{testimonial.name}</div>
          <div className="text-blue-600 font-semibold text-sm">{testimonial.business}</div>
          <div className="text-gray-500 text-sm">{testimonial.location}</div>
        </div>
      </div>
    </div>
  );

  return (
    <section className="py-20 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <BlurFade delay={0.1}>
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Real Results for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600">
                Real Contractors
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Don't just take our word for it. Our clients are experiencing real growth and efficiency with our systems. 
              Testimonials are absolutely key and are often the reason new clients come to us.
            </p>
            
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-50 to-blue-50 px-6 py-3 rounded-full border border-green-200">
              <Star className="w-5 h-5 text-green-600" />
              <span className="text-green-800 font-semibold">Over 500 successful contractor transformations</span>
            </div>
          </div>
        </BlurFade>

        {/* Animated Marquee Testimonials */}
        <BlurFade delay={0.2}>
          <div className="relative">
            <Marquee pauseOnHover className="[--duration:20s]">
              {firstRow.map((testimonial, index) => (
                <TestimonialCard key={index} testimonial={testimonial} />
              ))}
            </Marquee>
            <Marquee reverse pauseOnHover className="[--duration:20s]">
              {secondRow.map((testimonial, index) => (
                <TestimonialCard key={index} testimonial={testimonial} />
              ))}
            </Marquee>
            <div className="pointer-events-none absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-white dark:from-background"></div>
            <div className="pointer-events-none absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-white dark:from-background"></div>
          </div>
        </BlurFade>

        {/* Stats Section */}
        <BlurFade delay={0.3}>
          <div className="mt-20">
            <div className="bg-gradient-to-r from-blue-900 to-teal-800 rounded-3xl p-8 md:p-12 text-white">
              <h3 className="text-3xl md:text-4xl font-bold text-center mb-12">
                The Numbers Don't Lie
              </h3>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="text-4xl md:text-5xl font-bold text-green-400 mb-2">500+</div>
                  <div className="text-blue-100 font-semibold">Happy Contractors</div>
                </div>
                
                <div className="text-center">
                  <div className="text-4xl md:text-5xl font-bold text-green-400 mb-2">3.2x</div>
                  <div className="text-blue-100 font-semibold">Average Lead Increase</div>
                </div>
                
                <div className="text-center">
                  <div className="text-4xl md:text-5xl font-bold text-green-400 mb-2">89%</div>
                  <div className="text-blue-100 font-semibold">Client Retention Rate</div>
                </div>
                
                <div className="text-center">
                  <div className="text-4xl md:text-5xl font-bold text-green-400 mb-2">24/7</div>
                  <div className="text-blue-100 font-semibold">Lead Capture</div>
                </div>
              </div>
            </div>
          </div>
        </BlurFade>

        {/* Social Proof */}
        <BlurFade delay={0.4}>
          <div className="text-center mt-16">
            <p className="text-lg text-gray-600 mb-8">
              Our clients regularly share their success stories on social media and recommend us to other contractors. 
              Check out our Instagram for more real results and behind-the-scenes content.
            </p>
            
            <div className="flex justify-center space-x-8 text-gray-400">
              <div className="flex items-center space-x-2">
                <Star className="w-5 h-5 text-yellow-400 fill-current" />
                <span className="text-gray-700 font-semibold">4.9/5 Average Rating</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">📈</span>
                <span className="text-gray-700 font-semibold">Average 320% ROI</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">⚡</span>
                <span className="text-gray-700 font-semibold">Results in 30 Days</span>
              </div>
            </div>
          </div>
        </BlurFade>
      </div>
    </section>
  );
};

export default Testimonials;