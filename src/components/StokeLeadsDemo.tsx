"use client";

import React from "react";
import { StokeLeadsAnimatedBeam } from "@/components/ui/animated-beam-template";

export function StokeLeadsDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center p-8">
      <div className="max-w-6xl w-full">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            All Your Messages Flow to{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-red-600">
              StokeLeads
            </span>
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Watch how every customer touchpoint flows seamlessly into your unified StokeLeads dashboard, 
            then gets delivered directly to contractors. Never miss a lead, no matter where it comes from.
          </p>
        </div>
        
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
          <StokeLeadsAnimatedBeam />
        </div>
        
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📱</span>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Multiple Channels</h3>
            <p className="text-gray-600">
              Capture leads from Google Business, Facebook Messenger, Instagram, SMS, phone calls, and email.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🎯</span>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Unified Dashboard</h3>
            <p className="text-gray-600">
              All messages flow into StokeLeads where they're organized, tracked, and managed efficiently.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🔧</span>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Direct to Contractors</h3>
            <p className="text-gray-600">
              Qualified leads are instantly delivered to the right contractors for immediate follow-up.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
