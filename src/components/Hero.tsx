import React, { useState } from 'react';
import { ArrowRight, Star, Users, Zap, CheckCircle, Phone } from 'lucide-react';
import BlurFade from './ui/blur-fade';
import GridPattern from './ui/grid-pattern';

const Hero = () => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubmitted(true);
      // Here you would typically send the email to your backend
      console.log('Email submitted:', email);
    }
  };

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-teal-700 overflow-hidden">
      {/* Magic UI Grid Pattern Background */}
      <GridPattern
        width={60}
        height={60}
        x={-1}
        y={-1}
        strokeDasharray={0}
        numSquares={30}
        className="absolute inset-0 opacity-20 [mask-image:radial-gradient(500px_circle_at_center,white,transparent)]"
        maxOpacity={0.1}
      />
      
      {/* Gradient accent shapes */}
      <div className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full opacity-20 blur-xl"></div>
      <div className="absolute bottom-40 left-10 w-24 h-24 bg-gradient-to-br from-teal-400 to-blue-500 rounded-full opacity-25 blur-lg"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header with Logo */}
        <BlurFade delay={0.1}>
          <div className="flex items-center justify-between mb-16">
            <div className="flex items-center space-x-4">
              <img 
                src="https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6823a53b9c2a113deb622364.webp" 
                alt="StokeLeads Logo" 
                className="h-12 w-auto"
              />
            </div>
            
            {/* Trust Indicators */}
            <div className="hidden md:flex items-center space-x-6 text-sm">
              <div className="flex items-center space-x-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <span className="text-blue-200">5.0 Rating</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-blue-300" />
                <span className="text-blue-200">500+ Contractors</span>
              </div>
            </div>
          </div>
        </BlurFade>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Content */}
          <div className="text-white space-y-8">
            {/* Main Headlines - With Blur Fade Animation */}
            <BlurFade delay={0.2}>
              <div className="space-y-4">
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                  Is Your Outdated Website{' '}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-yellow-400">
                    Costing You Customers?
                  </span>
                </h1>
                <h2 className="text-xl md:text-2xl text-blue-100 font-medium leading-relaxed">
                  Get a FREE, AI-Powered Site That Works!
                </h2>
              </div>
            </BlurFade>

            {/* Value Proposition */}
            <BlurFade delay={0.3}>
              <p className="text-lg md:text-xl text-blue-100 leading-relaxed max-w-2xl">
                We build modern, mobile-responsive websites that capture leads, automate 5-star Google reviews, 
                and connect directly to your phone, so you make more money without the hassle.
              </p>
            </BlurFade>

            {/* Key Benefits */}
            <BlurFade delay={0.4}>
              <div className="grid md:grid-cols-2 gap-4">
                {[
                  "Never Miss a Lead",
                  "Automated 5-Star Reviews", 
                  "Auto Text-Back System",
                  "Unified Lead Management"
                ].map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <Zap className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-blue-100">{benefit}</span>
                  </div>
                ))}
              </div>
            </BlurFade>

            {/* CTA Buttons */}
            <BlurFade delay={0.5}>
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="group bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl flex items-center justify-center space-x-2">
                  <span>Claim Your FREE Website Today!</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </button>
                <button className="border-2 border-white text-white hover:bg-white hover:text-blue-900 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 flex items-center justify-center space-x-2">
                  <Phone className="w-5 h-5" />
                  <span>Book Free Strategy Call</span>
                </button>
              </div>
            </BlurFade>

            {/* Trust Badge */}
            <BlurFade delay={0.6}>
              <p className="text-sm text-blue-200">
                <strong>Trusted by contractors nationwide.</strong> Don't pay thousands for a website that doesn't deliver.
              </p>
            </BlurFade>
          </div>

          {/* Right Column - Form */}
          <BlurFade delay={0.7}>
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-6 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                {!isSubmitted ? (
                  <>
                    <div className="text-center mb-6">
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">
                        Claim Your FREE Website
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        Join hundreds of contractors who are already growing their businesses.
                      </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                          Enter Your Email Address
                        </label>
                        <input
                          type="email"
                          id="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="<EMAIL>"
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
                          required
                        />
                      </div>

                      <button
                        type="submit"
                        className="w-full group bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-2"
                      >
                        <span>Get My FREE Website Now!</span>
                        <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                      </button>
                    </form>

                    <div className="mt-4 text-center">
                      <p className="text-xs text-gray-500">
                        ✓ No credit card required • ✓ Built in 24 hours • ✓ 100% Free to start
                      </p>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-6">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="w-6 h-6 text-green-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      Thank You!
                    </h3>
                    <p className="text-gray-600 mb-4">
                      We'll be in touch within 24 hours to get your free website started.
                    </p>
                    <button
                      onClick={() => setIsSubmitted(false)}
                      className="text-orange-600 font-semibold hover:underline"
                    >
                      Submit Another Email
                    </button>
                  </div>
                )}
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 bg-green-500 text-white p-3 rounded-full shadow-lg animate-bounce">
                <Star className="w-6 h-6" />
              </div>
              <div className="absolute -bottom-4 -left-4 bg-orange-500 text-white p-3 rounded-full shadow-lg animate-pulse">
                <Phone className="w-6 h-6" />
              </div>
            </div>
          </BlurFade>
        </div>
      </div>
    </section>
  );
};

export default Hero;