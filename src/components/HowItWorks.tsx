import React from 'react';
import { DollarSign, Zap, TrendingUp, Shield } from 'lucide-react';

const HowItWorks = () => {
  const benefits = [
    {
      icon: DollarSign,
      title: "No Massive Upfront Cost",
      description: "Get a top-tier website without paying thousands upfront"
    },
    {
      icon: Zap,
      title: "Instant Setup",
      description: "AI-powered website creation in minutes, not weeks"
    },
    {
      icon: TrendingUp,
      title: "Everything Included",
      description: "Hosting, security, maintenance, and all features included"
    },
    {
      icon: Shield,
      title: "Risk-Free",
      description: "Start with a free website and see results before you commit"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 to-blue-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            The Smart, <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400">Risk-Free</span> Way to Get Online & Grow
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Traditional web design can cost thousands upfront. We offer a different approach that gets you 
            results without the massive risk.
          </p>
        </div>

        {/* How It Works Steps */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {/* Step 1 */}
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-6 shadow-xl">
              <span className="text-3xl font-bold">1</span>
            </div>
            <h3 className="text-2xl font-bold mb-4">Get Your FREE Website</h3>
            <p className="text-gray-300 leading-relaxed">
              We build you a high-quality, fully functional website for FREE using our AI-powered templating system. 
              This isn't just a website; it's a pre-built system that works for you and your small business.
            </p>
          </div>

          {/* Step 2 */}
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full mb-6 shadow-xl">
              <span className="text-3xl font-bold">2</span>
            </div>
            <h3 className="text-2xl font-bold mb-4">See It Work for You</h3>
            <p className="text-gray-300 leading-relaxed">
              Watch as leads start flowing in, reviews get automated, and your business becomes more efficient. 
              Experience the power of having a system that actually makes you money.
            </p>
          </div>

          {/* Step 3 */}
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full mb-6 shadow-xl">
              <span className="text-3xl font-bold">3</span>
            </div>
            <h3 className="text-2xl font-bold mb-4">Pay Only When You're Happy</h3>
            <p className="text-gray-300 leading-relaxed">
              Once you see the results, you pay a simple monthly fee that covers hosting, security, maintenance, 
              and access to all the powerful features that keep making you money.
            </p>
          </div>
        </div>

        {/* Pricing Card */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
            <div className="bg-gradient-to-r from-green-500 to-blue-600 p-8 text-center">
              <h3 className="text-3xl font-bold mb-2">Complete Business System</h3>
              <p className="text-green-100 text-lg">Everything you need to grow your contractor business</p>
            </div>
            
            <div className="p-8 text-gray-800">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h4 className="text-2xl font-bold mb-6 text-center">What's Included</h4>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                      <span>Professional AI-built website</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                      <span>Lead capture & AI chatbot</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                      <span>5-star review automation</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                      <span>Missed call text-back system</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                      <span>Unified mobile app dashboard</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                      <span>Text remarketing campaigns</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                      <span>Calendar integration</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                      <span>Payment processing</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                      <span>Hosting, security & maintenance</span>
                    </div>
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="mb-8">
                    <div className="text-5xl font-bold text-gray-900 mb-2">$297</div>
                    <div className="text-lg text-gray-600">per month</div>
                    <div className="text-sm text-gray-500">after your FREE trial period</div>
                  </div>
                  
                  <div className="bg-gradient-to-r from-red-50 to-orange-50 border-l-4 border-orange-500 p-4 mb-6">
                    <p className="text-sm text-gray-700">
                      <strong>Compare:</strong> Traditional web design costs $3,000-$10,000 upfront, 
                      plus monthly hosting and maintenance fees - without any of these powerful automation features!
                    </p>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-6">
                    All they care about is that you save them time and money, and they will pay you every single month 
                    for low-ticket monthly recurring revenue that gives you freedom and lifestyle.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mt-16">
          {benefits.map((benefit, index) => (
            <div key={index} className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-10 rounded-full mb-4">
                <benefit.icon className="w-8 h-8 text-green-400" />
              </div>
              <h3 className="text-xl font-bold mb-3">{benefit.title}</h3>
              <p className="text-gray-300 leading-relaxed">{benefit.description}</p>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-2xl font-bold text-green-400">
            Get a top-tier online presence and lead generation system without the massive upfront cost!
          </p>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;