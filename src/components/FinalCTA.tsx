import React from 'react';
import { ArrowR<PERSON>, Phone, Clock, DollarSign, TrendingUp } from 'lucide-react';

const FinalCTA = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-teal-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/background-pattern.svg')] opacity-20"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center text-white mb-16">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            Stop Leaving Money{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-yellow-400">
              on the Table
            </span>
          </h2>
          <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
            It's time your online presence truly worked for your business. Get your free AI-powered website 
            and start capturing more leads, automating your reviews, and streamlining your operations today.
          </p>
        </div>

        {/* Urgency Indicators */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="text-center text-white">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-red-500 rounded-full mb-4">
              <Clock className="w-8 h-8" />
            </div>
            <h3 className="text-xl font-bold mb-2">Every Day You Wait</h3>
            <p className="text-blue-200">You're losing potential customers to competitors with better online presence</p>
          </div>
          
          <div className="text-center text-white">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-500 rounded-full mb-4">
              <DollarSign className="w-8 h-8" />
            </div>
            <h3 className="text-xl font-bold mb-2">Average Cost</h3>
            <p className="text-blue-200">Contractors lose $5,000+ per month in missed opportunities without proper systems</p>
          </div>
          
          <div className="text-center text-white">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-500 rounded-full mb-4">
              <TrendingUp className="w-8 h-8" />
            </div>
            <h3 className="text-xl font-bold mb-2">Start Today</h3>
            <p className="text-blue-200">See results within the first week and grow your business exponentially</p>
          </div>
        </div>

        {/* Main CTA Section */}
        <div className="bg-white rounded-3xl shadow-2xl p-8 md:p-12 max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-xl text-gray-600 leading-relaxed">
              Join hundreds of contractors who are already growing their businesses with our AI-powered system.
            </p>
          </div>

          {/* Primary CTA */}
          <div className="space-y-6">
            <button className="w-full group bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-12 py-6 rounded-2xl font-bold text-xl transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3">
              <span>Claim My FREE Website & System</span>
              <ArrowRight className="w-6 h-6 group-hover:translate-x-2 transition-transform" />
            </button>

            {/* Secondary CTA */}
            <div className="text-center">
              <p className="text-gray-600 mb-4">or</p>
              <button className="group border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 flex items-center justify-center space-x-2 mx-auto">
                <Phone className="w-5 h-5" />
                <span>Book a Free Strategy Call</span>
              </button>
            </div>
          </div>

          {/* Contact Form */}
          <div className="mt-12 p-8 bg-gray-50 rounded-2xl">
            <h4 className="text-2xl font-bold text-gray-900 text-center mb-6">
              Get Started in 60 Seconds
            </h4>
            
            <form className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <input 
                  type="text" 
                  placeholder="Your Name" 
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
                />
                <input 
                  type="email" 
                  placeholder="Email Address" 
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
                />
              </div>
              
              <div className="grid md:grid-cols-2 gap-4">
                <input 
                  type="tel" 
                  placeholder="Phone Number" 
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
                />
                <select className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all">
                  <option>Select Your Trade</option>
                  <option>Roofing</option>
                  <option>Landscaping</option>
                  <option>Plumbing</option>
                  <option>HVAC</option>
                  <option>Remodeling</option>
                  <option>Security</option>
                  <option>Other</option>
                </select>
              </div>
              
              <textarea 
                placeholder="Tell us about your business and goals (optional)" 
                rows={3}
                className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all resize-none"
              ></textarea>
              
              <button 
                type="submit" 
                className="w-full bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Get My Free Website Now!
              </button>
            </form>
            
            <p className="text-sm text-gray-500 text-center mt-4">
              * No credit card required. Your website will be built and ready in under 24 hours.
            </p>
          </div>
        </div>

        {/* Final Trust Elements */}
        <div className="text-center mt-16 text-white">
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            <div className="flex items-center justify-center space-x-2">
              <span className="text-green-400 text-2xl">✓</span>
              <span className="text-blue-100">No Setup Fees</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <span className="text-green-400 text-2xl">✓</span>
              <span className="text-blue-100">30-Day Money Back Guarantee</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <span className="text-green-400 text-2xl">✓</span>
              <span className="text-blue-100">Cancel Anytime</span>
            </div>
          </div>
          
          <p className="text-2xl font-bold text-green-400 mb-2">
            Don't let another day pass watching competitors steal your customers!
          </p>
          <p className="text-lg text-blue-200">
            Take action now and transform your business with the power of AI.
          </p>
        </div>
      </div>
    </section>
  );
};

export default FinalCTA;