import { cn } from "@/lib/utils";
import React from "react";

interface Iphone15ProProps {
  className?: string;
  children?: React.ReactNode;
}

const Iphone15Pro = React.forwardRef<HTMLDivElement, Iphone15ProProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "relative mx-auto border-gray-800 dark:border-gray-800 bg-gray-800 border-[14px] rounded-[2.5rem] h-[600px] w-[300px]",
          className
        )}
        {...props}
      >
        {/* Dynamic Island */}
        <div className="w-[148px] h-[18px] bg-gray-800 top-[18px] rounded-b-[1rem] left-1/2 -translate-x-1/2 absolute"></div>
        
        {/* Left side buttons */}
        <div className="h-[46px] w-[3px] bg-gray-800 absolute -start-[17px] top-[124px] rounded-s-lg"></div>
        <div className="h-[46px] w-[3px] bg-gray-800 absolute -start-[17px] top-[178px] rounded-s-lg"></div>
        <div className="h-[64px] w-[3px] bg-gray-800 absolute -start-[17px] top-[235px] rounded-s-lg"></div>
        
        {/* Right side button */}
        <div className="h-[46px] w-[3px] bg-gray-800 absolute -end-[17px] top-[142px] rounded-e-lg"></div>
        
        {/* Screen */}
        <div className="rounded-[2rem] overflow-hidden w-[272px] h-[572px] bg-white dark:bg-gray-800 relative">
          {children}
        </div>
      </div>
    );
  }
);

Iphone15Pro.displayName = "Iphone15Pro";

export default Iphone15Pro;
