import { cn } from "@/lib/utils";
import React from "react";

interface Iphone15ProProps {
  className?: string;
  children?: React.ReactNode;
}

const Iphone15Pro = React.forwardRef<HTMLDivElement, Iphone15ProProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "relative mx-auto bg-gray-900 rounded-[2.5rem] h-[600px] w-[300px] shadow-xl border-[12px] border-gray-900",
          className
        )}
        {...props}
      >
        {/* Dynamic Island */}
        <div className="w-[148px] h-[18px] bg-gray-900 top-[6px] rounded-b-[1rem] left-1/2 -translate-x-1/2 absolute z-10"></div>

        {/* Screen */}
        <div className="rounded-[2rem] overflow-hidden w-full h-full bg-white relative">
          {children}
        </div>
      </div>
    );
  }
);

Iphone15Pro.displayName = "Iphone15Pro";

export default Iphone15Pro;
