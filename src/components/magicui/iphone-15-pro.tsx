import { cn } from "@/lib/utils";
import React from "react";

interface Iphone15ProProps {
  className?: string;
  children?: React.ReactNode;
}

const Iphone15Pro = React.forwardRef<HTMLDivElement, Iphone15ProProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "relative mx-auto bg-white rounded-[4rem] h-[1000px] w-[480px] shadow-2xl border border-gray-200",
          className
        )}
        {...props}
      >
        {/* Dynamic Island */}
        <div className="w-[160px] h-[32px] bg-black top-[20px] rounded-full left-1/2 -translate-x-1/2 absolute"></div>

        {/* Screen Content */}
        <div className="pt-20 px-8 h-full overflow-hidden">
          {children}
        </div>
      </div>
    );
  }
);

Iphone15Pro.displayName = "Iphone15Pro";

export default Iphone15Pro;
