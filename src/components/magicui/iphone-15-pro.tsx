import { cn } from "@/lib/utils";
import React from "react";

interface Iphone15ProProps {
  className?: string;
  children?: React.ReactNode;
}

const Iphone15Pro = React.forwardRef<HTMLDivElement, Iphone15ProProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "relative mx-auto bg-black rounded-[2.5rem] h-[600px] w-[300px] shadow-xl",
          className
        )}
        {...props}
      >
        {/* Dynamic Island */}
        <div className="w-[148px] h-[18px] bg-black top-[18px] rounded-b-[1rem] left-1/2 -translate-x-1/2 absolute z-10"></div>

        {/* Screen */}
        <div className="rounded-[2rem] overflow-hidden w-[272px] h-[572px] bg-white absolute top-[14px] left-[14px]">
          {children}
        </div>
      </div>
    );
  }
);

Iphone15Pro.displayName = "Iphone15Pro";

export default Iphone15Pro;
