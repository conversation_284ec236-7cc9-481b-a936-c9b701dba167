import React, { useRef } from "react";
import { MessageSquare, Mail, Phone, Star, Instagram, User } from "lucide-react";
import BlurFade from "./ui/blur-fade";
import { AnimatedBeam } from "./ui/animated-beam";

const Circle = React.forwardRef<
  HTMLDivElement,
  { className?: string; children?: React.ReactNode }
>(({ className, children }, ref) => {
  return (
    <div
      ref={ref}
      className={`z-10 flex size-12 items-center justify-center rounded-full border-2 bg-white p-3 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)] ${className || ''}`}
    >
      {children}
    </div>
  );
});

Circle.displayName = "Circle";

const AnimatedBeamSection = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const div1Ref = useRef<HTMLDivElement>(null); // Facebook
  const div2Ref = useRef<HTMLDivElement>(null); // Instagram
  const div3Ref = useRef<HTMLDivElement>(null); // Google Reviews
  const div4Ref = useRef<HTMLDivElement>(null); // SMS
  const div5Ref = useRef<HTMLDivElement>(null); // Email
  const div6Ref = useRef<HTMLDivElement>(null); // Phone
  const div7Ref = useRef<HTMLDivElement>(null); // Center - StokeLeads App
  const div8Ref = useRef<HTMLDivElement>(null); // Output - User/Contractor

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <BlurFade delay={0.1}>
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              All Your Messages in{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-teal-600">
                One Place
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Watch how every customer touchpoint flows seamlessly into your unified StokeLeads dashboard. 
              Never miss a lead, no matter where it comes from.
            </p>
          </div>
        </BlurFade>

        <BlurFade delay={0.2}>
          <div 
            className="relative flex h-[500px] w-full items-center justify-center overflow-hidden rounded-2xl bg-white shadow-xl border border-gray-200 p-10"
            ref={containerRef}
          >
            <div className="flex size-full max-w-5xl flex-row items-stretch justify-between gap-10">
              {/* Left side - Input sources (logos only) */}
              <div className="flex flex-col justify-center gap-6">
                <Circle ref={div1Ref} className="border-blue-500">
                  <MessageSquare className="h-5 w-5 text-blue-600" />
                </Circle>
                
                <Circle ref={div2Ref} className="border-purple-500">
                  <Instagram className="h-5 w-5 text-purple-600" />
                </Circle>
                
                <Circle ref={div3Ref} className="border-green-500">
                  <Star className="h-5 w-5 text-green-600" />
                </Circle>
                
                <Circle ref={div4Ref} className="border-teal-500">
                  <MessageSquare className="h-5 w-5 text-teal-600" />
                </Circle>
                
                <Circle ref={div5Ref} className="border-gray-500">
                  <Mail className="h-5 w-5 text-gray-600" />
                </Circle>
                
                <Circle ref={div6Ref} className="border-red-500">
                  <Phone className="h-5 w-5 text-red-600" />
                </Circle>
              </div>

              {/* Center - StokeLeads App */}
              <div className="flex flex-col justify-center items-center">
                <Circle ref={div7Ref} className="size-16 border-orange-500">
                  <img 
                    src="/square logo icon (3).webp" 
                    alt="StokeLeads App" 
                    className="h-8 w-8 object-contain"
                  />
                </Circle>
                <div className="mt-4 text-center">
                  <div className="text-sm font-bold text-orange-600">StokeLeads Mobile App</div>
                  <div className="text-xs text-gray-500">All messages unified</div>
                </div>
              </div>

              {/* Right side - Output (Contractor/User) */}
              <div className="flex flex-col justify-center items-center">
                <Circle ref={div8Ref} className="border-gray-400">
                  <User className="h-5 w-5 text-gray-600" />
                </Circle>
                <div className="mt-4 text-center">
                  <div className="text-sm font-semibold text-gray-600">You (Contractor)</div>
                </div>
              </div>
            </div>

            {/* Animated Beams - Inputs to Center with Curvature */}
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div1Ref}
              toRef={div7Ref}
              curvature={-75}
              duration={3}
              delay={0}
              gradientStartColor="#3b82f6"
              gradientStopColor="#1d4ed8"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div2Ref}
              toRef={div7Ref}
              curvature={-50}
              duration={3}
              delay={0.5}
              gradientStartColor="#a855f7"
              gradientStopColor="#7c3aed"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div3Ref}
              toRef={div7Ref}
              curvature={-25}
              duration={3}
              delay={1}
              gradientStartColor="#16a34a"
              gradientStopColor="#15803d"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div4Ref}
              toRef={div7Ref}
              curvature={25}
              duration={3}
              delay={1.5}
              gradientStartColor="#0d9488"
              gradientStopColor="#0f766e"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div5Ref}
              toRef={div7Ref}
              curvature={50}
              duration={3}
              delay={2}
              gradientStartColor="#374151"
              gradientStopColor="#1f2937"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div6Ref}
              toRef={div7Ref}
              curvature={75}
              duration={3}
              delay={2.5}
              gradientStartColor="#dc2626"
              gradientStopColor="#b91c1c"
            />

            {/* Animated Beam - Center to Output (slight curve) */}
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div7Ref}
              toRef={div8Ref}
              curvature={-20}
              duration={3}
              delay={3}
              gradientStartColor="#ea580c"
              gradientStopColor="#c2410c"
            />
          </div>
        </BlurFade>

        {/* Benefits Grid */}
        <BlurFade delay={0.3}>
          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <MessageSquare className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Never Miss a Message</h3>
              <p className="text-gray-600 leading-relaxed">
                Every customer inquiry from every platform flows directly into your mobile app. 
                Respond instantly, no matter where the message originated.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                <Phone className="w-6 h-6 text-teal-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Instant Notifications</h3>
              <p className="text-gray-600 leading-relaxed">
                Get real-time alerts for every new lead. Whether it's a Facebook message, 
                Google review, or missed call - you'll know immediately.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <Star className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Organized & Efficient</h3>
              <p className="text-gray-600 leading-relaxed">
                All conversations are organized by customer and project. 
                Track the entire customer journey from first contact to completed job.
              </p>
            </div>
          </div>
        </BlurFade>

        {/* Call to Action */}
        <BlurFade delay={0.4}>
          <div className="text-center mt-16">
            <div className="bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white max-w-3xl mx-auto">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                Ready to Unify Your Communications?
              </h3>
              <p className="text-xl text-blue-100 mb-6 leading-relaxed">
                Stop juggling multiple apps and missing important messages. 
                Get everything in one powerful, easy-to-use mobile dashboard.
              </p>
              <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg">
                Get My FREE Unified System
              </button>
            </div>
          </div>
        </BlurFade>
      </div>
    </section>
  );
};

export default AnimatedBeamSection;