import React, { useRef } from "react";
import { MessageSquare, Mail, Phone, Star, Instagram, User } from "lucide-react";
import BlurFade from "./ui/blur-fade";
import { AnimatedBeam } from "./ui/animated-beam";

const Circle = React.forwardRef<
  HTMLDivElement,
  { className?: string; children?: React.ReactNode }
>(({ className, children }, ref) => {
  return (
    <div
      ref={ref}
      className={`z-10 flex size-12 items-center justify-center rounded-full border-2 bg-white p-3 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)] ${className || ''}`}
    >
      {children}
    </div>
  );
});

Circle.displayName = "Circle";

const AnimatedBeamSection = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const div1Ref = useRef<HTMLDivElement>(null); // Facebook
  const div2Ref = useRef<HTMLDivElement>(null); // Instagram
  const div3Ref = useRef<HTMLDivElement>(null); // Google Reviews
  const div4Ref = useRef<HTMLDivElement>(null); // SMS
  const div5Ref = useRef<HTMLDivElement>(null); // Email
  const div6Ref = useRef<HTMLDivElement>(null); // Phone
  const div7Ref = useRef<HTMLDivElement>(null); // Center - StokeLeads App
  const div8Ref = useRef<HTMLDivElement>(null); // Output - User/Contractor

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <BlurFade delay={0.1}>
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              All Your Messages in{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-teal-600">
                One Place
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Watch how every customer touchpoint flows seamlessly into your unified StokeLeads dashboard. 
              Never miss a lead, no matter where it comes from.
            </p>
          </div>
        </BlurFade>

        <BlurFade delay={0.2}>
          <div 
            className="relative flex h-[500px] w-full items-center justify-center overflow-hidden rounded-2xl bg-white shadow-xl border border-gray-200 p-10"
            ref={containerRef}
          >
            <div className="flex size-full max-w-5xl flex-row items-stretch justify-between gap-10">
              {/* Left side - Input sources (branded logos) */}
              <div className="flex flex-col justify-center gap-6">
                <Circle ref={div1Ref} className="border-gray-200">
                  <Icons.googleDrive />
                </Circle>

                <Circle ref={div2Ref} className="border-gray-200">
                  <Icons.googleDocs />
                </Circle>

                <Circle ref={div3Ref} className="border-gray-200">
                  <Icons.whatsapp />
                </Circle>

                <Circle ref={div4Ref} className="border-gray-200">
                  <Icons.messenger />
                </Circle>

                <Circle ref={div5Ref} className="border-gray-200">
                  <Icons.notion />
                </Circle>
              </div>

              {/* Center - Hub */}
              <div className="flex flex-col justify-center items-center">
                <Circle ref={div7Ref} className="size-16 border-gray-200">
                  <Icons.centerHub />
                </Circle>
              </div>

              {/* Right side - Output (User) */}
              <div className="flex flex-col justify-center items-center">
                <Circle ref={div8Ref} className="border-gray-200">
                  <Icons.user />
                </Circle>
              </div>
            </div>

            {/* Animated Beams - Inputs to Center with Curvature */}
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div1Ref}
              toRef={div7Ref}
              curvature={-75}
              duration={3}
              delay={0}
              gradientStartColor="#3b82f6"
              gradientStopColor="#1d4ed8"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div2Ref}
              toRef={div7Ref}
              curvature={-50}
              duration={3}
              delay={0.5}
              gradientStartColor="#a855f7"
              gradientStopColor="#7c3aed"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div3Ref}
              toRef={div7Ref}
              curvature={-25}
              duration={3}
              delay={1}
              gradientStartColor="#16a34a"
              gradientStopColor="#15803d"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div4Ref}
              toRef={div7Ref}
              curvature={25}
              duration={3}
              delay={1.5}
              gradientStartColor="#0d9488"
              gradientStopColor="#0f766e"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div5Ref}
              toRef={div7Ref}
              curvature={50}
              duration={3}
              delay={2}
              gradientStartColor="#374151"
              gradientStopColor="#1f2937"
            />
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div6Ref}
              toRef={div7Ref}
              curvature={75}
              duration={3}
              delay={2.5}
              gradientStartColor="#dc2626"
              gradientStopColor="#b91c1c"
            />

            {/* Animated Beam - Center to Output (slight curve) */}
            <AnimatedBeam
              containerRef={containerRef}
              fromRef={div7Ref}
              toRef={div8Ref}
              curvature={-20}
              duration={3}
              delay={3}
              gradientStartColor="#ea580c"
              gradientStopColor="#c2410c"
            />
          </div>
        </BlurFade>

        {/* Benefits Grid */}
        <BlurFade delay={0.3}>
          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <MessageSquare className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Never Miss a Message</h3>
              <p className="text-gray-600 leading-relaxed">
                Every customer inquiry from every platform flows directly into your mobile app. 
                Respond instantly, no matter where the message originated.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                <Phone className="w-6 h-6 text-teal-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Instant Notifications</h3>
              <p className="text-gray-600 leading-relaxed">
                Get real-time alerts for every new lead. Whether it's a Facebook message, 
                Google review, or missed call - you'll know immediately.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <Star className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Organized & Efficient</h3>
              <p className="text-gray-600 leading-relaxed">
                All conversations are organized by customer and project. 
                Track the entire customer journey from first contact to completed job.
              </p>
            </div>
          </div>
        </BlurFade>

        {/* Call to Action */}
        <BlurFade delay={0.4}>
          <div className="text-center mt-16">
            <div className="bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white max-w-3xl mx-auto">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                Ready to Unify Your Communications?
              </h3>
              <p className="text-xl text-blue-100 mb-6 leading-relaxed">
                Stop juggling multiple apps and missing important messages. 
                Get everything in one powerful, easy-to-use mobile dashboard.
              </p>
              <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg">
                Get My FREE Unified System
              </button>
            </div>
          </div>
        </BlurFade>
      </div>
    </section>
  );
};

const Icons = {
  googleBusiness: () => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 87.3 78"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="m6.6 66.85 3.85 6.65c.8 1.4 1.95 2.5 3.3 3.3l13.75-23.8h-27.5c0 1.55.4 3.1 1.2 4.5z"
        fill="#0066da"
      />
      <path
        d="m43.65 25-13.75-23.8c-1.35.8-2.5 1.9-3.3 3.3l-25.4 44a9.06 9.06 0 0 0 -1.2 4.5h27.5z"
        fill="#00ac47"
      />
      <path
        d="m73.55 76.8c1.35-.8 2.5-1.9 3.3-3.3l1.6-2.75 7.65-13.25c.8-1.4 1.2-2.95 1.2-4.5h-27.502l5.852 11.5z"
        fill="#ea4335"
      />
      <path
        d="m43.65 25 13.75-23.8c-1.35-.8-2.9-1.2-4.5-1.2h-18.5c-1.6 0-3.15.45-4.5 1.2z"
        fill="#00832d"
      />
      <path
        d="m59.8 53h-32.3l-13.75 23.8c1.35.8 2.9 1.2 4.5 1.2h50.8c1.6 0 3.15-.45 4.5-1.2z"
        fill="#2684fc"
      />
      <path
        d="m73.4 26.5-12.7-22c-.8-1.4-1.95-2.5-3.3-3.3l-13.75 23.8 16.15 28h27.45c0-1.55-.4-3.1-1.2-4.5z"
        fill="#ffba00"
      />
    </svg>
  ),
  messenger: () => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 48 48"
      xmlns="http://www.w3.org/2000/svg"
    >
      <radialGradient
        id="8O3wK6b5ASW2Wn6hRCB5xa_YFbzdUk7Q3F8_gr1"
        cx="11.087"
        cy="7.022"
        r="47.612"
        gradientTransform="matrix(1 0 0 -1 0 50)"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor="#1292ff"></stop>
        <stop offset=".079" stopColor="#2982ff"></stop>
        <stop offset=".23" stopColor="#4e69ff"></stop>
        <stop offset=".351" stopColor="#6559ff"></stop>
        <stop offset=".428" stopColor="#6d53ff"></stop>
        <stop offset=".754" stopColor="#df47aa"></stop>
        <stop offset=".946" stopColor="#ff6257"></stop>
      </radialGradient>
      <path
        fill="url(#8O3wK6b5ASW2Wn6hRCB5xa_YFbzdUk7Q3F8_gr1)"
        d="M44,23.5C44,34.27,35.05,43,24,43c-1.651,0-3.25-0.194-4.784-0.564	c-0.465-0.112-0.951-0.069-1.379,0.145L13.46,44.77C12.33,45.335,11,44.513,11,43.249v-4.025c0-0.575-0.257-1.111-0.681-1.499	C6.425,34.165,4,29.11,4,23.5C4,12.73,12.95,4,24,4S44,12.73,44,23.5z"
      />
      <path
        fill="#ffffff"
        d="M34.394,18.501l-5.7,4.22c-0.61,0.46-1.44,0.46-2.04,0.01L22.68,19.74	c-1.68-1.25-4.06-0.82-5.19,0.94l-1.21,1.89l-4.11,6.68c-0.6,0.94,0.55,2.01,1.44,1.34l5.7-4.22c0.61-0.46,1.44-0.46,2.04-0.01	l3.974,2.991c1.68,1.25,4.06,0.82,5.19-0.94l1.21-1.89l4.11-6.68C36.434,18.901,35.284,17.831,34.394,18.501z"
      />
    </svg>
  ),
  instagram: () => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
        fill="url(#instagram-gradient)"
      />
      <defs>
        <linearGradient id="instagram-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#833ab4" />
          <stop offset="50%" stopColor="#fd1d1d" />
          <stop offset="100%" stopColor="#fcb045" />
        </linearGradient>
      </defs>
    </svg>
  ),
  sms: () => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H5.17L4 17.17V4H20V16Z"
        fill="#25D366"
      />
      <path
        d="M7 9H17V11H7V9ZM7 12H15V14H7V12Z"
        fill="#25D366"
      />
    </svg>
  ),
  phone: () => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.62 10.79C8.06 13.62 10.38 15.94 13.21 17.38L15.41 15.18C15.69 14.9 16.08 14.82 16.43 14.93C17.55 15.3 18.75 15.5 20 15.5C20.55 15.5 21 15.95 21 16.5V20C21 20.55 20.55 21 20 21C10.61 21 3 13.39 3 4C3 3.45 3.45 3 4 3H7.5C8.05 3 8.5 3.45 8.5 4C8.5 5.25 8.7 6.45 9.07 7.57C9.18 7.92 9.1 8.31 8.82 8.59L6.62 10.79Z"
        fill="#34C759"
      />
    </svg>
  ),
  email: () => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"
        fill="#FF6B35"
      />
    </svg>
  ),
};

export default AnimatedBeamSection;